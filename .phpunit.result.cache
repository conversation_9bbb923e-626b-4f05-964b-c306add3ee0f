{"version": 1, "defects": {"Tests\\Unit\\Models\\UserTest::test_user_can_be_created_with_basic_attributes": 8, "Tests\\Unit\\Models\\UserTest::test_user_role_slug_attribute": 8, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_candidate": 8, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_recruiter": 8, "Tests\\Unit\\Models\\UserTest::test_user_firstname": 8, "Tests\\Unit\\Models\\UserTest::test_user_civility_relationship": 8, "Tests\\Unit\\Models\\UserTest::test_user_phones_relationship": 8, "Tests\\Unit\\Models\\UserTest::test_user_get_address": 8, "Tests\\Unit\\Models\\UserTest::test_user_get_phone_number": 8, "Tests\\Unit\\Models\\UserTest::test_user_suspension": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_one_relationship": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_two_relationship": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_messages_relationship": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_last_message": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_with_no_messages": 8, "Tests\\Unit\\Models\\MessageTest::test_message_can_be_created": 8, "Tests\\Unit\\Models\\MessageTest::test_message_conversation_relationship": 8, "Tests\\Unit\\Models\\MessageTest::test_message_sender_relationship": 8, "Tests\\Unit\\Models\\MessageTest::test_message_read_status": 8, "Tests\\Unit\\Models\\MessageTest::test_message_with_different_senders": 8, "Tests\\Unit\\Models\\MessageTest::test_message_content_validation": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_can_be_created": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_different_statuses": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_trial_period": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_quantity": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_update": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_deletion": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_fillable_attributes": 8}, "times": {"Tests\\Unit\\Models\\UserTest::test_user_can_be_created_with_basic_attributes": 0.006, "Tests\\Unit\\Models\\UserTest::test_user_role_slug_attribute": 0.008, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_candidate": 0.01, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_recruiter": 0.011, "Tests\\Unit\\Models\\UserTest::test_user_firstname": 0.007, "Tests\\Unit\\Models\\UserTest::test_user_civility_relationship": 0.006, "Tests\\Unit\\Models\\UserTest::test_user_phones_relationship": 0.007, "Tests\\Unit\\Models\\UserTest::test_user_get_address": 0.007, "Tests\\Unit\\Models\\UserTest::test_user_get_phone_number": 0.008, "Tests\\Unit\\Models\\UserTest::test_user_suspension": 0.007, "Tests\\Unit\\Models\\ConversationTest::test_conversation_can_be_created": 0.024, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_one_relationship": 0.009, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_two_relationship": 0.006, "Tests\\Unit\\Models\\ConversationTest::test_conversation_messages_relationship": 0.006, "Tests\\Unit\\Models\\ConversationTest::test_conversation_last_message": 0.006, "Tests\\Unit\\Models\\ConversationTest::test_conversation_with_no_messages": 0.006, "Tests\\Unit\\Models\\MessageTest::test_message_can_be_created": 0.006, "Tests\\Unit\\Models\\MessageTest::test_message_conversation_relationship": 0.008, "Tests\\Unit\\Models\\MessageTest::test_message_sender_relationship": 0.007, "Tests\\Unit\\Models\\MessageTest::test_message_read_status": 0.007, "Tests\\Unit\\Models\\MessageTest::test_message_with_different_senders": 0.006, "Tests\\Unit\\Models\\MessageTest::test_message_content_validation": 0.006, "Tests\\Unit\\Models\\PackageTest::test_package_can_be_created_with_basic_attributes": 0.005, "Tests\\Unit\\Models\\PackageTest::test_package_features_array_casting": 0.004, "Tests\\Unit\\Models\\PackageTest::test_package_tax_included_boolean_casting": 0.004, "Tests\\Unit\\Models\\PackageTest::test_package_with_different_currencies": 0.003, "Tests\\Unit\\Models\\PackageTest::test_package_with_zero_price": 0.005, "Tests\\Unit\\Models\\PackageTest::test_package_with_high_price": 0.004, "Tests\\Unit\\Models\\PackageTest::test_package_update": 0.006, "Tests\\Unit\\Models\\PackageTest::test_package_deletion": 0.006, "Tests\\Unit\\Models\\PackageTest::test_package_with_empty_features": 0.003, "Tests\\Unit\\Models\\PackageTest::test_package_fillable_attributes": 0.003, "Tests\\Unit\\Models\\PlanTest::test_plan_can_be_created_with_basic_attributes": 0.004, "Tests\\Unit\\Models\\PlanTest::test_plan_with_different_durations": 0.004, "Tests\\Unit\\Models\\PlanTest::test_plan_with_different_currencies": 0.005, "Tests\\Unit\\Models\\PlanTest::test_plan_with_html_description": 0.004, "Tests\\Unit\\Models\\PlanTest::test_plan_slug_uniqueness": 0.004, "Tests\\Unit\\Models\\PlanTest::test_plan_update": 0.006, "Tests\\Unit\\Models\\PlanTest::test_plan_deletion": 0.004, "Tests\\Unit\\Models\\PlanTest::test_plan_with_zero_price": 0.003, "Tests\\Unit\\Models\\PlanTest::test_plan_fillable_attributes": 0.003, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_can_be_created": 0.006, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_different_statuses": 0.006, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_trial_period": 0.006, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_quantity": 0.008, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_update": 0.006, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_deletion": 0.007, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_fillable_attributes": 0.006}}
{"version": 1, "defects": {"Tests\\Unit\\Models\\UserTest::test_user_can_be_created_with_basic_attributes": 7, "Tests\\Unit\\Models\\UserTest::test_user_role_slug_attribute": 8, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_candidate": 8, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_recruiter": 8, "Tests\\Unit\\Models\\UserTest::test_user_firstname": 8, "Tests\\Unit\\Models\\UserTest::test_user_civility_relationship": 8, "Tests\\Unit\\Models\\UserTest::test_user_phones_relationship": 8, "Tests\\Unit\\Models\\UserTest::test_user_get_address": 8, "Tests\\Unit\\Models\\UserTest::test_user_get_phone_number": 8, "Tests\\Unit\\Models\\UserTest::test_user_suspension": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_one_relationship": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_two_relationship": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_messages_relationship": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_last_message": 8, "Tests\\Unit\\Models\\ConversationTest::test_conversation_with_no_messages": 8, "Tests\\Unit\\Models\\MessageTest::test_message_can_be_created": 8, "Tests\\Unit\\Models\\MessageTest::test_message_conversation_relationship": 8, "Tests\\Unit\\Models\\MessageTest::test_message_sender_relationship": 8, "Tests\\Unit\\Models\\MessageTest::test_message_read_status": 8, "Tests\\Unit\\Models\\MessageTest::test_message_with_different_senders": 8, "Tests\\Unit\\Models\\MessageTest::test_message_content_validation": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_can_be_created": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_different_statuses": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_trial_period": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_quantity": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_update": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_deletion": 8, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_fillable_attributes": 8, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_throws_403_for_unauthorized_role": 7, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 7, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 7, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 8, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_logout": 8, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 7, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 8, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_is_not_verified_with_invalid_hash": 8, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 7, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 7, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 8, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 7, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 7, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 7, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 7, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_password_can_be_updated": 7, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_correct_password_must_be_provided_to_update_password": 8, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 7, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 7, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_page_loads_successfully": 7, "Tests\\Feature\\CandidateRegistrationTest::test_successful_candidate_registration": 7, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_with_invalid_date_of_birth": 7, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_without_accepting_terms": 8, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 7, "Tests\\Feature\\MessagingSystemTest::test_creating_new_conversation_between_users": 7, "Tests\\Feature\\MessagingSystemTest::test_getting_existing_conversation_instead_of_creating_duplicate": 7, "Tests\\Feature\\MessagingSystemTest::test_sending_message_in_existing_conversation": 7, "Tests\\Feature\\MessagingSystemTest::test_sending_message_to_candidate_creates_new_conversation": 7, "Tests\\Feature\\MessagingSystemTest::test_getting_messages_from_conversation": 7, "Tests\\Feature\\MessagingSystemTest::test_marking_messages_as_read": 7, "Tests\\Feature\\MessagingSystemTest::test_getting_user_conversations": 7, "Tests\\Feature\\ProfileTest::test_profile_page_is_displayed": 7, "Tests\\Feature\\ProfileTest::test_profile_information_can_be_updated": 8, "Tests\\Feature\\ProfileTest::test_email_verification_status_is_unchanged_when_the_email_address_is_unchanged": 8, "Tests\\Feature\\ProfileTest::test_user_can_delete_their_account": 7, "Tests\\Feature\\ProfileTest::test_correct_password_must_be_provided_to_delete_account": 8}, "times": {"Tests\\Unit\\Models\\UserTest::test_user_can_be_created_with_basic_attributes": 0.007, "Tests\\Unit\\Models\\UserTest::test_user_role_slug_attribute": 0.01, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_candidate": 0.011, "Tests\\Unit\\Models\\UserTest::test_user_fullname_for_recruiter": 0.009, "Tests\\Unit\\Models\\UserTest::test_user_firstname": 0.009, "Tests\\Unit\\Models\\UserTest::test_user_civility_relationship": 0.007, "Tests\\Unit\\Models\\UserTest::test_user_phones_relationship": 0.008, "Tests\\Unit\\Models\\UserTest::test_user_get_address": 0.01, "Tests\\Unit\\Models\\UserTest::test_user_get_phone_number": 0.01, "Tests\\Unit\\Models\\UserTest::test_user_suspension": 0.008, "Tests\\Unit\\Models\\ConversationTest::test_conversation_can_be_created": 0.011, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_one_relationship": 0.013, "Tests\\Unit\\Models\\ConversationTest::test_conversation_user_two_relationship": 0.013, "Tests\\Unit\\Models\\ConversationTest::test_conversation_messages_relationship": 0.012, "Tests\\Unit\\Models\\ConversationTest::test_conversation_last_message": 1.013, "Tests\\Unit\\Models\\ConversationTest::test_conversation_with_no_messages": 0.013, "Tests\\Unit\\Models\\MessageTest::test_message_can_be_created": 0.014, "Tests\\Unit\\Models\\MessageTest::test_message_conversation_relationship": 0.014, "Tests\\Unit\\Models\\MessageTest::test_message_sender_relationship": 0.011, "Tests\\Unit\\Models\\MessageTest::test_message_read_status": 0.012, "Tests\\Unit\\Models\\MessageTest::test_message_with_different_senders": 0.011, "Tests\\Unit\\Models\\MessageTest::test_message_content_validation": 0.011, "Tests\\Unit\\Models\\PackageTest::test_package_can_be_created_with_basic_attributes": 0.005, "Tests\\Unit\\Models\\PackageTest::test_package_features_array_casting": 0.004, "Tests\\Unit\\Models\\PackageTest::test_package_tax_included_boolean_casting": 0.007, "Tests\\Unit\\Models\\PackageTest::test_package_with_different_currencies": 0.005, "Tests\\Unit\\Models\\PackageTest::test_package_with_zero_price": 0.004, "Tests\\Unit\\Models\\PackageTest::test_package_with_high_price": 0.004, "Tests\\Unit\\Models\\PackageTest::test_package_update": 0.005, "Tests\\Unit\\Models\\PackageTest::test_package_deletion": 0.005, "Tests\\Unit\\Models\\PackageTest::test_package_with_empty_features": 0.003, "Tests\\Unit\\Models\\PackageTest::test_package_fillable_attributes": 0.006, "Tests\\Unit\\Models\\PlanTest::test_plan_can_be_created_with_basic_attributes": 0.008, "Tests\\Unit\\Models\\PlanTest::test_plan_with_different_durations": 0.006, "Tests\\Unit\\Models\\PlanTest::test_plan_with_different_currencies": 0.006, "Tests\\Unit\\Models\\PlanTest::test_plan_with_html_description": 0.004, "Tests\\Unit\\Models\\PlanTest::test_plan_slug_uniqueness": 0.005, "Tests\\Unit\\Models\\PlanTest::test_plan_update": 0.008, "Tests\\Unit\\Models\\PlanTest::test_plan_deletion": 0.005, "Tests\\Unit\\Models\\PlanTest::test_plan_with_zero_price": 0.003, "Tests\\Unit\\Models\\PlanTest::test_plan_fillable_attributes": 0.005, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_can_be_created": 0.011, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_different_statuses": 0.014, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_with_trial_period": 0.008, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_quantity": 0.009, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_update": 0.008, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_deletion": 0.009, "Tests\\Unit\\Models\\SubscriptionTest::test_subscription_fillable_attributes": 0.02, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_allows_access_for_non_suspended_users": 0.024, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_allows_access_for_unauthenticated_users": 0.003, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_redirects_suspended_users_to_suspension_page": 0.007, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_logs_out_suspended_users": 0.005, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_with_candidate_suspended_user": 0.005, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_with_recruiter_suspended_user": 0.006, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_with_admin_suspended_user": 0.006, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_with_user_suspended_during_session": 0.007, "Tests\\Unit\\Middleware\\CheckSuspensionTest::test_middleware_with_null_is_suspend_value": 0.005, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_allows_access_for_candidates": 0.007, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_allows_access_for_subscribed_recruiters": 0.009, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_allows_access_for_recruiters_during_free_trial": 0.01, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_redirects_unsubscribed_recruiters_to_packages_page": 0.007, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_redirects_recruiters_when_trial_has_expired": 0.008, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_with_admin_user": 0.009, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_with_recruiter_having_canceled_subscription": 0.013, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_with_multiple_candidates": 0.011, "Tests\\Unit\\Middleware\\IsSubscribedOrCandidateTest::test_middleware_with_mixed_user_types_in_sequence": 0.013, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_allows_access_for_users_with_active_subscription": 0.007, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_redirects_users_without_subscription_to_packages_page": 0.007, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_allows_access_during_free_trial_period": 0.009, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_redirects_when_free_trial_has_expired": 0.015, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_redirects_when_banner_is_disabled": 0.011, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_redirects_when_no_trial_configuration_exists": 0.008, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_with_canceled_subscription": 0.017, "Tests\\Unit\\Middleware\\IsSubscribedTest::test_middleware_redirects_unauthenticated_users": 0.004, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_redirects_unauthenticated_users_to_login": 0.004, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_allows_access_for_correct_role": 0.009, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_allows_access_for_multiple_roles": 0.008, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_redirects_admin_to_admin_dashboard": 0.01, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_redirects_recruiter_to_recruiter_dashboard": 0.008, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_redirects_candidate_to_candidate_dashboard": 0.008, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_admin_accessing_admin_route": 0.011, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_with_multiple_allowed_roles": 0.023, "Tests\\Unit\\Middleware\\RoleMiddlewareTest::test_middleware_throws_403_for_unauthorized_role": 0.012, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.002, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 0.111, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 0.025, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 0.008, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_logout": 0.008, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 0.062, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 0.014, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_is_not_verified_with_invalid_hash": 0.016, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 0.059, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 0.212, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 0.21, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 0.058, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 0.018, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 0.011, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 0.011, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_password_can_be_updated": 0.012, "Tests\\Feature\\Auth\\PasswordUpdateTest::test_correct_password_must_be_provided_to_update_password": 0.01, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 0.078, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 0.044, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_page_loads_successfully": 0.197, "Tests\\Feature\\CandidateRegistrationTest::test_successful_candidate_registration": 0.017, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_with_missing_required_fields": 0.01, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_with_invalid_email": 0.009, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_with_duplicate_email": 0.012, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_with_password_mismatch": 0.009, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_with_invalid_date_of_birth": 0.01, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_with_invalid_category": 0.008, "Tests\\Feature\\CandidateRegistrationTest::test_candidate_registration_without_accepting_terms": 0.007, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.091, "Tests\\Feature\\MessagingSystemTest::test_creating_new_conversation_between_users": 0.017, "Tests\\Feature\\MessagingSystemTest::test_getting_existing_conversation_instead_of_creating_duplicate": 0.014, "Tests\\Feature\\MessagingSystemTest::test_sending_message_in_existing_conversation": 0.015, "Tests\\Feature\\MessagingSystemTest::test_sending_message_to_candidate_creates_new_conversation": 0.012, "Tests\\Feature\\MessagingSystemTest::test_getting_messages_from_conversation": 0.013, "Tests\\Feature\\MessagingSystemTest::test_marking_messages_as_read": 0.014, "Tests\\Feature\\MessagingSystemTest::test_getting_user_conversations": 0.016, "Tests\\Feature\\ProfileTest::test_profile_page_is_displayed": 0.061, "Tests\\Feature\\ProfileTest::test_profile_information_can_be_updated": 0.012, "Tests\\Feature\\ProfileTest::test_email_verification_status_is_unchanged_when_the_email_address_is_unchanged": 0.01, "Tests\\Feature\\ProfileTest::test_user_can_delete_their_account": 0.01, "Tests\\Feature\\ProfileTest::test_correct_password_must_be_provided_to_delete_account": 0.01}}
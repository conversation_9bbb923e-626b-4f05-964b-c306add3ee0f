<?php

namespace Tests;

use Illuminate\Support\Facades\DB;
use MongoDB\Client;

trait DatabaseTestTrait
{
    /**
     * Setup the test database.
     */
    protected function setUpDatabase(): void
    {
        // Ensure we're using the test database connection
        config(['database.default' => 'mongodb_testing']);
        
        // Clear the test database before each test
        $this->clearTestDatabase();
    }

    /**
     * Clear the test database.
     */
    protected function clearTestDatabase(): void
    {
        try {
            // Clear collections using Laravel MongoDB methods
            $collections = ['users', 'roles', 'civilities', 'phones', 'addresses', 'conversations', 'messages', 'packages', 'plans', 'subscriptions', 'config_global_apps'];

            foreach ($collections as $collection) {
                try {
                    DB::connection('mongodb_testing')->table($collection)->truncate();
                } catch (\Exception $e) {
                    // Collection might not exist, that's fine
                }
            }
        } catch (\Exception $e) {
            // If database doesn't exist or connection fails, that's fine for tests
        }
    }

    /**
     * Tear down the test database.
     */
    protected function tearDownDatabase(): void
    {
        $this->clearTestDatabase();
    }

    /**
     * Create basic roles for testing.
     */
    protected function createBasicRoles(): void
    {
        $roles = [
            ['name' => 'candidate', 'slug' => 'candidate'],
            ['name' => 'recruter', 'slug' => 'recruter'],
            ['name' => 'admin', 'slug' => 'admin'],
        ];

        foreach ($roles as $role) {
            DB::connection('mongodb_testing')->table('roles')->insert($role);
        }
    }

    /**
     * Get a test user ID.
     */
    protected function getTestUserId(): string
    {
        return '507f1f77bcf86cd799439011';
    }

    /**
     * Get another test user ID.
     */
    protected function getAnotherTestUserId(): string
    {
        return '507f1f77bcf86cd799439012';
    }
}

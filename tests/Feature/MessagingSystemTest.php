<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\FeatureTestTrait;
use App\Models\User;
use App\Models\Role;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\ConfigGlobalApp;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class MessagingSystemTest extends TestCase
{
    use FeatureTestTrait;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpFeatureTest();
    }

    protected function tearDown(): void
    {
        $this->tearDownFeatureTest();
        parent::tearDown();
    }

    /**
     * Test creating a new conversation between users.
     */
    public function test_creating_new_conversation_between_users()
    {
        $this->createBasicRoles();
        $this->setupSubscribedRecruiter();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create subscription for recruiter
        $this->createActiveSubscription($recruiter);

        $this->actingAs($recruiter);

        $response = $this->postJson('/conversations', [
            'user_id' => $candidate->id,
        ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'user_one_id',
            'user_two_id',
            'created_at',
            'updated_at',
        ]);

        // Verify conversation was created in database
        $this->assertDatabaseHas('conversations', [
            'user_one_id' => $recruiter->id,
            'user_two_id' => $candidate->id,
        ]);
    }

    /**
     * Test getting existing conversation instead of creating duplicate.
     */
    public function test_getting_existing_conversation_instead_of_creating_duplicate()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create subscription for recruiter
        $this->createActiveSubscription($recruiter);

        // Create existing conversation
        $existingConversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $this->actingAs($recruiter);

        $response = $this->postJson('/conversations', [
            'user_id' => $candidate->id,
        ]);

        $response->assertStatus(201);
        $response->assertJson([
            'id' => $existingConversation->id,
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Verify no duplicate conversation was created
        $conversationCount = Conversation::where(function ($query) use ($candidate, $recruiter) {
            $query->where('user_one_id', $candidate->id)->where('user_two_id', $recruiter->id);
        })->orWhere(function ($query) use ($candidate, $recruiter) {
            $query->where('user_one_id', $recruiter->id)->where('user_two_id', $candidate->id);
        })->count();

        $this->assertEquals(1, $conversationCount);
    }

    /**
     * Test sending a message in existing conversation.
     */
    public function test_sending_message_in_existing_conversation()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create subscription for recruiter
        $this->createActiveSubscription($recruiter);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $this->actingAs($recruiter);

        $response = $this->postJson('/messages/send', [
            'conversation_id' => $conversation->id,
            'content' => 'Hello, I am interested in your profile.',
        ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'conversation_id',
            'sender_id',
            'content',
            'is_read',
            'created_at',
            'updated_at',
        ]);

        // Verify message was created in database
        $this->assertDatabaseHas('messages', [
            'conversation_id' => $conversation->id,
            'sender_id' => $recruiter->id,
            'content' => 'Hello, I am interested in your profile.',
            'is_read' => false,
        ]);
    }

    /**
     * Test sending message to candidate creates new conversation.
     */
    public function test_sending_message_to_candidate_creates_new_conversation()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create subscription for recruiter
        $this->createActiveSubscription($recruiter);

        $this->actingAs($recruiter);

        $response = $this->postJson('/messages/send', [
            'candidate_id' => $candidate->id,
            'content' => 'Hello, I would like to discuss a job opportunity.',
        ]);

        $response->assertStatus(201);

        // Verify conversation was created
        $this->assertDatabaseHas('conversations', [
            'user_one_id' => $recruiter->id,
            'user_two_id' => $candidate->id,
        ]);

        // Verify message was created
        $conversation = Conversation::where('user_one_id', $recruiter->id)
            ->where('user_two_id', $candidate->id)
            ->first();

        $this->assertDatabaseHas('messages', [
            'conversation_id' => $conversation->id,
            'sender_id' => $recruiter->id,
            'content' => 'Hello, I would like to discuss a job opportunity.',
            'is_read' => false,
        ]);
    }

    /**
     * Test getting messages from a conversation.
     */
    public function test_getting_messages_from_conversation()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create subscription for recruiter
        $this->createActiveSubscription($recruiter);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Create messages
        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $recruiter->id,
            'content' => 'First message',
            'is_read' => false,
        ]);

        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Second message',
            'is_read' => false,
        ]);

        $this->actingAs($recruiter);

        $response = $this->getJson("/messages/{$conversation->id}");

        $response->assertStatus(200);
        $response->assertJsonCount(2);
        $response->assertJsonFragment(['content' => 'First message']);
        $response->assertJsonFragment(['content' => 'Second message']);
    }

    /**
     * Test marking messages as read.
     */
    public function test_marking_messages_as_read()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create subscription for recruiter
        $this->createActiveSubscription($recruiter);

        $conversation = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        // Create unread messages
        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Unread message 1',
            'is_read' => false,
        ]);

        Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $candidate->id,
            'content' => 'Unread message 2',
            'is_read' => false,
        ]);

        $this->actingAs($recruiter);

        $response = $this->patchJson("/messages/{$conversation->id}/read");

        $response->assertStatus(200);
        $response->assertJson(['status' => 'Messages marked as read']);

        // Verify messages are marked as read
        $unreadCount = Message::where('conversation_id', $conversation->id)
            ->where('is_read', false)
            ->count();

        $this->assertEquals(0, $unreadCount);
    }

    /**
     * Test getting user conversations.
     */
    public function test_getting_user_conversations()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();
        $recruiterRole = Role::where('slug', 'recruter')->first();

        $candidate = User::create([
            'name' => 'Jane Candidate',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $recruiter = User::create([
            'name' => 'Bob Recruiter',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $recruiterRole->id,
        ]);

        // Create subscription for recruiter
        $this->createActiveSubscription($recruiter);

        // Create conversations
        $conversation1 = Conversation::create([
            'user_one_id' => $candidate->id,
            'user_two_id' => $recruiter->id,
        ]);

        $conversation2 = Conversation::create([
            'user_one_id' => $recruiter->id,
            'user_two_id' => $candidate->id,
        ]);

        $this->actingAs($recruiter);

        $response = $this->getJson('/conversations');

        $response->assertStatus(200);
        $response->assertJsonCount(2);
    }

    /**
     * Helper method to create active subscription for user.
     */
    private function createActiveSubscription(User $user)
    {
        $plan = Plan::create([
            'name' => 'Monthly Plan',
            'slug' => 'monthly-plan',
            'stripe_product_id' => 'prod_test123',
            'stripe_price_id' => 'price_test123',
            'price' => 29.99,
            'currency' => 'EUR',
            'duration_in_days' => 30,
        ]);

        Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'stripe_id' => 'sub_test123',
            'stripe_status' => 'active',
            'quantity' => 1,
        ]);
    }

    /**
     * Helper method to setup subscribed recruiter environment.
     */
    private function setupSubscribedRecruiter()
    {
        // Enable banner display
        ConfigGlobalApp::create([
            'name' => 'display_bandeau',
            'value' => true,
            'comment' => 'Display banner configuration',
        ]);

        // Set trial expiration to future date
        ConfigGlobalApp::create([
            'name' => 'day_free_after_publish',
            'value' => now()->addDays(15)->toDateTimeString(),
            'comment' => 'Free trial expiration date',
        ]);
    }
}

<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Civility;
use App\Models\Phone;
use App\Models\Address;
use App\Models\Country;
use App\Models\FieldActivity;
use App\Models\Profession;
use App\Models\TypeProfession;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;

class CandidateRegistrationTest extends TestCase
{
    /**
     * Test candidate registration page loads successfully.
     */
    public function test_candidate_registration_page_loads_successfully()
    {
        $response = $this->get('/inscription-candidat');

        $response->assertStatus(200);
        $response->assertViewIs('candidate.register');
    }

    /**
     * Test successful candidate registration.
     */
    public function test_successful_candidate_registration()
    {
        $this->createBasicRoles();
        $this->createBasicData();

        $country = Country::create(['name' => 'Switzerland', 'code' => 'CH']);
        $fieldActivity = FieldActivity::create(['name' => 'Technology', 'slug' => 'technology']);
        $profession = Profession::create(['name' => 'Developer', 'slug' => 'developer', 'field_activity_id' => $fieldActivity->id]);
        $typeProfession = TypeProfession::create(['name' => 'Full-time', 'slug' => 'full-time']);

        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41123456789',
            'vehicle' => 'yes',
            'permits' => [],
            'residence_permit' => 'permit_b',
            'criminal_record' => 'no',
            'country_of_residence' => $country->id,
            'commune' => 'Geneva',
            'address' => '123 Main Street',
            'latitude_longitude' => '46.2044,6.1432',
            'activity_fields' => [$fieldActivity->id],
            'professions_list' => [$profession->id],
            'open_professions' => 'yes',
            'job_types' => [$typeProfession->id],
            'contract_type' => ['cdi'],
            'responsibility' => 'team_leader',
            'work_rate' => '100',
            'native_language' => 'french',
            'fluent_languages' => ['english'],
            'intermediate_languages' => ['german'],
            'basic_languages' => ['italian'],
            'terms' => true,
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertRedirect('/candidats/dashboard');

        // Verify user was created
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);

        // Verify user has candidate role
        $user = User::where('email', '<EMAIL>')->first();
        $candidateRole = Role::where('slug', 'candidate')->first();
        $this->assertEquals($candidateRole->id, $user->role_id);

        // Verify civility was created
        $this->assertDatabaseHas('civilities', [
            'user_id' => $user->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        // Verify phone was created
        $this->assertDatabaseHas('phones', [
            'user_id' => $user->id,
            'number' => '+41123456789',
        ]);

        // Verify address was created
        $this->assertDatabaseHas('addresses', [
            'user_id' => $user->id,
            'name' => '123 Main Street',
        ]);
    }

    /**
     * Test candidate registration with missing required fields.
     */
    public function test_candidate_registration_with_missing_required_fields()
    {
        $this->createBasicRoles();

        $registrationData = [
            'email' => '<EMAIL>',
            // Missing password
            'first_name' => 'John',
            // Missing last_name
            // Missing other required fields
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertSessionHasErrors([
            'password',
            'last_name',
            'date_of_birth',
            'category',
            'phone',
        ]);

        // Verify user was not created
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test candidate registration with invalid email.
     */
    public function test_candidate_registration_with_invalid_email()
    {
        $this->createBasicRoles();

        $registrationData = [
            'email' => 'invalid-email',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertSessionHasErrors(['email']);

        // Verify user was not created
        $this->assertDatabaseMissing('users', [
            'email' => 'invalid-email',
        ]);
    }

    /**
     * Test candidate registration with duplicate email.
     */
    public function test_candidate_registration_with_duplicate_email()
    {
        $this->createBasicRoles();

        $candidateRole = Role::where('slug', 'candidate')->first();

        // Create existing user
        User::create([
            'name' => 'Existing User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role_id' => $candidateRole->id,
        ]);

        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertSessionHasErrors(['email']);
    }

    /**
     * Test candidate registration with password mismatch.
     */
    public function test_candidate_registration_with_password_mismatch()
    {
        $this->createBasicRoles();

        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'different_password',
            'first_name' => 'John',
            'last_name' => 'Doe',
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertSessionHasErrors(['password']);

        // Verify user was not created
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test candidate registration with invalid date of birth.
     */
    public function test_candidate_registration_with_invalid_date_of_birth()
    {
        $this->createBasicRoles();

        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '2025-01-01', // Future date
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertSessionHasErrors(['date_of_birth']);
    }

    /**
     * Test candidate registration with invalid category.
     */
    public function test_candidate_registration_with_invalid_category()
    {
        $this->createBasicRoles();

        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'invalid_category',
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertSessionHasErrors(['category']);
    }

    /**
     * Test candidate registration without accepting terms.
     */
    public function test_candidate_registration_without_accepting_terms()
    {
        $this->createBasicRoles();
        $this->createBasicData();

        $country = Country::create(['name' => 'Switzerland', 'code' => 'CH']);
        $fieldActivity = FieldActivity::create(['name' => 'Technology', 'slug' => 'technology']);
        $profession = Profession::create(['name' => 'Developer', 'slug' => 'developer', 'field_activity_id' => $fieldActivity->id]);
        $typeProfession = TypeProfession::create(['name' => 'Full-time', 'slug' => 'full-time']);

        $registrationData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'date_of_birth' => '1990-01-01',
            'category' => 'current_profiles',
            'phone' => '+41123456789',
            'vehicle' => 'yes',
            'permits' => [],
            'residence_permit' => 'permit_b',
            'criminal_record' => 'no',
            'country_of_residence' => $country->id,
            'commune' => 'Geneva',
            'address' => '123 Main Street',
            'latitude_longitude' => '46.2044,6.1432',
            'activity_fields' => [$fieldActivity->id],
            'professions_list' => [$profession->id],
            'open_professions' => 'yes',
            'job_types' => [$typeProfession->id],
            'contract_type' => ['cdi'],
            'responsibility' => 'team_leader',
            'work_rate' => '100',
            'native_language' => 'french',
            'fluent_languages' => ['english'],
            'intermediate_languages' => ['german'],
            'basic_languages' => ['italian'],
            // Missing 'terms' => true,
        ];

        $response = $this->post('/inscription-candidat', $registrationData);

        $response->assertSessionHasErrors(['terms']);

        // Verify user was not created
        $this->assertDatabaseMissing('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Helper method to create basic data for tests.
     */
    private function createBasicData()
    {
        // This method can be expanded to create necessary test data
        // like countries, field activities, professions, etc.
    }
}
